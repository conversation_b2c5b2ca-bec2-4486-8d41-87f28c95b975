stages:
  - build
  - deploy
variables:
  GIT_DEPTH: "1"

before_script:
  - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY

.build-image:
  stage: build
  script:
    - docker build --pull -t $TARGET_IMAGE .
    - docker push $TARGET_IMAGE

build-x86:
  extends: .build-image
  tags:
    - docker
  variables:
    TARGET_IMAGE: $CI_REGISTRY_IMAGE:$CI_COMMIT_REF_SLUG

.deploy:
  stage: deploy
  tags:
    - docker
  script:
    - docker pull $CI_REGISTRY_IMAGE:$CI_COMMIT_REF_SLUG
    - docker tag $CI_REGISTRY_IMAGE:$CI_COMMIT_REF_SLUG $TARGET_IMAGE
    - docker push $TARGET_IMAGE

deploy_latest:
  extends: .deploy
  variables:
    TARGET_IMAGE: $CI_REGISTRY_IMAGE:latest
  only:
    - main

deploy_branch:
  extends: .deploy
  variables:
    TARGET_IMAGE: $CI_REGISTRY_IMAGE:$CI_COMMIT_REF_SLUG
