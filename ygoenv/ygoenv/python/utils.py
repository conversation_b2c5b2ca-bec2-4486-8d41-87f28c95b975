# Copyright 2021 Garena Online Private Limited
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
"""Helper function for Python API."""

from typing import Any, List

import numpy as np


def check_key_duplication(cls: Any, keytype: str, keys: List[str]) -> None:
  """Check if there's any duplicated keys in ``keys``."""
  ukeys, counts = np.unique(keys, return_counts=True)
  if not np.all(counts == 1):
    dup_keys = ukeys[counts > 1]
    raise SystemError(
      f"{cls} c++ code error. {keytype} keys {list(dup_keys)} are duplicated. "
      f"Please report to the author of {cls}."
    )
