#!/usr/bin/env python3
# Copyright 2021 Garena Online Private Limited
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
"""Dummy Env in EnvPool."""

from ygoenv.python.api import py_env

from .dummy_envpool import _DummyEnvPool, _DummyEnvSpec

DummyEnvSpec, DummyDMEnvPool, DummyGymEnvPool, DummyGymnasiumEnvPool = py_env(
  _DummyEnvSpec, _DummyEnvPool
)

__all__ = [
  "DummyEnvSpec",
  "DummyDMEnvPool",
  "DummyGymEnvPool",
  "DummyGymnasiumEnvPool",
]
