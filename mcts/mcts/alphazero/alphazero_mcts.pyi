from __future__ import annotations
import numpy
__all__ = ['MinMaxStatsList', 'Roots', 'SearchResults', 'batch_backpropagate', 'batch_expand', 'batch_traverse', 'init_module']
class MinMaxStatsList:
    def __init__(self, arg0: int) -> None:
        ...
    def set_delta(self, arg0: float) -> None:
        ...
class Roots:
    def __init__(self, arg0: int) -> None:
        ...
    def get_distributions(self) -> list[list[int]]:
        ...
    def get_values(self) -> list[float]:
        ...
    def prepare(self, arg0: numpy.ndarray[numpy.float32], arg1: numpy.ndarray[numpy.float32], arg2: numpy.ndarray[numpy.int32], arg3: numpy.ndarray[numpy.int32], arg4: float, arg5: float) -> None:
        ...
    @property
    def num(self) -> int:
        ...
class SearchResults:
    def __init__(self, arg0: int) -> None:
        ...
    def get_search_len(self) -> list[int]:
        ...
def batch_backpropagate(arg0: float, arg1: numpy.ndarray[numpy.float32], arg2: MinMaxStatsList, arg3: SearchResults) -> None:
    ...
def batch_expand(arg0: int, arg1: numpy.ndarray[bool], arg2: numpy.ndarray[numpy.float32], arg3: numpy.ndarray[numpy.float32], arg4: numpy.ndarray[numpy.int32], arg5: numpy.ndarray[numpy.int32], arg6: SearchResults) -> None:
    ...
def batch_traverse(arg0: Roots, arg1: int, arg2: float, arg3: float, arg4: MinMaxStatsList, arg5: SearchResults) -> tuple:
    ...
def init_module(seed: int) -> None:
    ...
